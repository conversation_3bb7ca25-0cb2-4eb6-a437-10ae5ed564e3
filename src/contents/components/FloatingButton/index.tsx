import React, { useState, useRef, useEffect } from 'react';
import { browser } from 'webextension-polyfill-ts';
import styles from './index.module.less';

interface FloatingButtonProps {
  onAction: (action: string) => void;
}

const FloatingButton: React.FC<FloatingButtonProps> = ({ onAction }) => {
  const [position, setPosition] = useState({ x: window.innerWidth - 60, y: window.innerHeight / 2 });
  const [isDragging, setIsDragging] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const buttonRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    const rect = buttonRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newX = Math.max(0, Math.min(window.innerWidth - 50, e.clientX - dragOffset.x));
      const newY = Math.max(0, Math.min(window.innerHeight - 50, e.clientY - dragOffset.y));
      setPosition({ x: newX, y: newY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isDragging) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleAction = (action: string) => {
    onAction(action);
    setIsExpanded(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  return (
    <div
      ref={buttonRef}
      className={`${styles.floatingButton} ${isDragging ? styles.dragging : ''}`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
    >
      <div className={styles.floatingMain}>
        <img
          src={browser.runtime.getURL('assets/icon.png')}
          alt="AI助手"
        />
      </div>

      {isExpanded && (
        <div className={`${styles.floatingMenu} ${isExpanded ? styles.show : ''}`}>
          <div
            className={styles.floatingMenuItem}
            onClick={() => handleAction('open-panel')}
          >
            🤖 AI 助手
          </div>
          <div
            className={styles.floatingMenuItem}
            onClick={() => handleAction('quick-translate')}
          >
            🌐 快速翻译
          </div>
          <div
            className={styles.floatingMenuItem}
            onClick={() => handleAction('quick-summary')}
          >
            📝 页面总结
          </div>
          <div
            className={styles.floatingMenuItem}
            onClick={() => handleAction('settings')}
          >
            ⚙️ 设置
          </div>
        </div>
      )}
    </div>
  );
};

export default FloatingButton;
