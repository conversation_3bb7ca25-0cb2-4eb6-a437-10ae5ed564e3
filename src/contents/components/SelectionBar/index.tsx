import React, { useState } from 'react';
import { browser } from 'webextension-polyfill-ts';

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = (action: string) => {
    onAction(action);
    if (action !== 'open-panel') {
      onClose();
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  return (
    <div className="selectionContainer">
      {/* 箭头指向选中文本 */}
      <div className="selectionArrow" />

      <div className="selectionBar">
      <div
        className="selectionIcon"
        onClick={() => handleAction('open-panel')}
        title="AI助手"
      >
        <img
          src={browser.runtime.getURL('assets/icon.png')}
          alt="AI"
        />
      </div>

      <div
        className="selectionButton"
        onClick={() => handleAction('polish')}
      >
        <img
          src={browser.runtime.getURL('assets/polish-icon.svg')}
          alt="润色"
        />
        <span>润色</span>
      </div>

      <div
        className="selectionButton"
        onClick={() => handleAction('translate')}
      >
        <img
          src={browser.runtime.getURL('assets/translate-icon.svg')}
          alt="翻译"
        />
        <span>翻译</span>
      </div>

      <div className="selectionMore">
        <div
          className="selectionDots"
          onClick={toggleDropdown}
        >
          ⋮
        </div>
        {showDropdown && (
          <div className={`selectionDropdown show`}>
            <div
              className="selectionDropdownItem"
              onClick={() => handleAction('summary')}
            >
              总结
            </div>
            <div
              className="selectionDropdownItem"
              onClick={() => handleAction('abbreviate')}
            >
              缩写
            </div>
            <div
              className="selectionDropdownItem"
              onClick={() => handleAction('expand')}
            >
              扩写
            </div>
            <div
              className="selectionDropdownItem"
              onClick={() => handleAction('correct')}
            >
              修正拼写和语义
            </div>
          </div>
        )}
      </div>

      {/* 分隔线 */}
      <div className="selectionDivider" />

      <div
        className="selectionClose"
        onClick={onClose}
        title="关闭"
      >
        ×
      </div>
      </div>
    </div>
  );
};

export default SelectionBar;
