import React, { useState } from 'react';
import { browser } from 'webextension-polyfill-ts';
import styleText from "data-text:./index.module.less";
import * as styles from "./index.module.less";
import type { PlasmoGetStyle } from "plasmo";

export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style");
  style.textContent = styleText;
  return style;
};

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = (action: string) => {
    onAction(action);
    if (action !== 'open-panel') {
      onClose();
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  return (
    <div className={styles.selectionContainer}>
      {/* 箭头指向选中文本 */}
      <div className={styles.selectionArrow} />

      <div className={styles.selectionBar}>
      <div
        className={styles.selectionIcon}
        onClick={() => handleAction('open-panel')}
        title="AI助手"
      >
        <img
          src={browser.runtime.getURL('assets/icon.png')}
          alt="AI"
        />
      </div>

      <div
        className={styles.selectionButton}
        onClick={() => handleAction('polish')}
      >
        <img
          src={browser.runtime.getURL('assets/polish-icon.svg')}
          alt="润色"
        />
        <span>润色</span>
      </div>

      <div
        className={styles.selectionButton}
        onClick={() => handleAction('translate')}
      >
        <img
          src={browser.runtime.getURL('assets/translate-icon.svg')}
          alt="翻译"
        />
        <span>翻译</span>
      </div>

      <div className={styles.selectionMore}>
        <div
          className={styles.selectionDots}
          onClick={toggleDropdown}
        >
          ⋮
        </div>
        {showDropdown && (
          <div className={`${styles.selectionDropdown} ${styles.show}`}>
            <div
              className={styles.selectionDropdownItem}
              onClick={() => handleAction('summary')}
            >
              总结
            </div>
            <div
              className={styles.selectionDropdownItem}
              onClick={() => handleAction('abbreviate')}
            >
              缩写
            </div>
            <div
              className={styles.selectionDropdownItem}
              onClick={() => handleAction('expand')}
            >
              扩写
            </div>
            <div
              className={styles.selectionDropdownItem}
              onClick={() => handleAction('correct')}
            >
              修正拼写和语义
            </div>
          </div>
        )}
      </div>

      {/* 分隔线 */}
      <div className={styles.selectionDivider} />

      <div
        className={styles.selectionClose}
        onClick={onClose}
        title="关闭"
      >
        ×
      </div>
      </div>
    </div>
  );
};

export default SelectionBar;
